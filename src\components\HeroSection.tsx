import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";

import ChatSimulation from "./ChatSimulation";
import AnimatedBackground from "./AnimatedBackground";
import ButtonJoinWaitingList from "./ButtonJoinWaitingList";

const YouTube = ({
  video,
  autoplay,
  rel,
  modest,
  placeholder,
  height = "400px",
  tittle,
}) => {
  const [status, setStatus] = useState("idle");

  return (
    <div className="relative w-full rounded-lg overflow-hidden shadow-lg bg-gray-100">
      {/* Aspect ratio container for responsive video */}
      <div
        className="relative w-full bg-cover bg-center bg-no-repeat"
        style={{
          aspectRatio: "16/9",
          minHeight: height,
          backgroundImage: `url('${placeholder}')`,
        }}
      >
        {status === "idle" ? (
          <div
            className="absolute inset-0 flex items-center justify-center cursor-pointer hover:bg-black/10 transition-colors"
            onClick={() => setStatus("playing")}
          >
            <button
              className="w-20 h-20 bg-red-600 hover:bg-red-700 text-white rounded-full flex items-center justify-center text-2xl shadow-lg transition-all hover:scale-105"
              aria-label="Play video"
            >
              ▶
            </button>
          </div>
        ) : (
          <iframe
            title={tittle || "My super video"}
            src={`https://www.youtube.com/embed/${video}?autoplay=${
              autoplay || false
            }&rel=${rel}&modestbranding=${modest}`}
            className="absolute inset-0 w-full h-full border-0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
        )}
      </div>
    </div>
  );
};

const HeroSection: React.FC = () => {
  return (
    <section className="relative bg-gradient-to-b from-white to-muted/30 overflow-hidden">
      {/* Animated background elements */}
      <AnimatedBackground />

      <div className="section-container">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-start">
          <div className="animate-fade-in">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-charcoal mb-6 font-poppins leading-tight">
              <span className="inline-block relative">
                <span className="relative z-10 bg-gradient-to-r from-orange-400 via-orange-500 to-pink-500 text-transparent bg-clip-text">
                  CatchUp
                </span>
              </span>
              <br />
              Sei stanco di perdere tempo in ricerche e prenotazioni? <br />
              Ci pensa il tuo assistente personale IA
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-muted-foreground relative">
              PARLA O CHATTA IN MODO SEMPLICE CON IL TUO ASSISTENTE IA CHE CERCA
              <span className="relative">
                {" "}
                LE MIGLIORI OFFERTE E LE PRENOTA PER TE RISPARMIA TEMPO E DENARO
                E ACCEDI A SERVIZI SELEZIONATI DI QUALITA’
                <span className="absolute -bottom-1 left-0 w-full h-1 bg-accent/30 rounded"></span>
              </span>
              <br />
            </p>


          </div>

          <div className="relative animate-fade-in delay-200">
            {/* Video Player aligned to top */}
            <div className="mb-6">
              <YouTube
                video="dQw4w9WgXcQ"
                autoplay={true}
                rel={false}
                modest={true}
                placeholder="/video/hero.mp4"
                tittle="CatchUp Demo Video"
              />
            </div>

            {/* Green area with promotional text */}
            <div className="relative p-6 rounded-lg border-1">
              <div className="text-center">
                <h2 className="text-lg md:text-xl font-bold text-charcoal mb-3 leading-tight">
                  VUOI ESSERE TRA I PRIMI 300 CATCHUP PIONEERS E AVERE PRIVILEGI
                  E ULTERIORI SCONTI?
                </h2>
                <p className="text-base md:text-lg font-semibold text-green-700 mb-4">
                  ISCRIVITI ORA ED ENTRA NELLA COMUNITY PREMIUM
                </p>
                <div className="flex flex-col items-center">
                  <ButtonJoinWaitingList analyticsLabel="pioneers-cta" />

                  {/* GRATIS badge positioned below the button */}
                  <div className="flex items-center mt-3 bg-gradient-to-r from-accent/10 to-accent/5 border border-accent/20 rounded-full px-3 py-1 shadow-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 text-accent mr-1"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-xs font-medium text-charcoal">
                      GRATIS
                    </span>
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-2 right-2 w-3 h-3 bg-green-400 rounded-full opacity-60"></div>
              <div className="absolute bottom-2 left-2 w-2 h-2 bg-green-400 rounded-full opacity-40"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
