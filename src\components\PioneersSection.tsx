import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ButtonJoinWaitingList from "./ButtonJoinWaitingList";

const PioneersSection: React.FC = () => {
  const [subscriberCount, setSubscriberCount] = useState(127); // Starting count
  const targetCount = 300;
  const remainingSpots = targetCount - subscriberCount;

  // Simulate real-time counter updates (optional)
  useEffect(() => {
    const interval = setInterval(() => {
      setSubscriberCount(prev => {
        if (prev < targetCount - 10) {
          // Randomly increment by 1-3 every few seconds
          const increment = Math.random() > 0.7 ? Math.floor(Math.random() * 3) + 1 : 0;
          return Math.min(prev + increment, targetCount - 10);
        }
        return prev;
      });
    }, 15000); // Update every 15 seconds

    return () => clearInterval(interval);
  }, [targetCount]);

  return (
    <section className="relative bg-gradient-to-r from-orange-50 via-orange-100 to-pink-50 py-16 md:py-20 overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 bg-orange-400 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-pink-400 rounded-full blur-xl"></div>
        <div className="absolute top-1/2 left-1/3 w-16 h-16 bg-orange-300 rounded-full blur-lg"></div>
      </div>

      <div className="section-container relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Main heading */}
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-charcoal mb-6 font-poppins leading-tight">
            <span className="bg-gradient-to-r from-orange-500 to-pink-500 text-transparent bg-clip-text">
              VUOI ESSERE TRA I PRIMI {targetCount} CATCHUP PIONEERS
            </span>
            <br />
            <span className="text-charcoal">
              E AVERE PRIVILEGI E ULTERIORI SCONTI?
            </span>
          </h2>

          {/* Subtitle */}
          <p className="text-xl md:text-2xl text-charcoal font-semibold mb-8">
            ISCRIVITI ORA ED ENTRA NELLA COMMUNITY PREMIUM
          </p>

          {/* Counter Section */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 mb-8 shadow-lg border border-orange-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
              {/* Current subscribers */}
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-orange-500 mb-2">
                  {subscriberCount}
                </div>
                <div className="text-sm md:text-base text-muted-foreground font-medium">
                  PIONEERS ISCRITTI
                </div>
              </div>

              {/* Progress bar */}
              <div className="text-center">
                <div className="w-full bg-gray-200 rounded-full h-4 mb-3">
                  <div 
                    className="bg-gradient-to-r from-orange-400 to-pink-500 h-4 rounded-full transition-all duration-1000 ease-out"
                    style={{ width: `${(subscriberCount / targetCount) * 100}%` }}
                  ></div>
                </div>
                <div className="text-sm text-muted-foreground">
                  {Math.round((subscriberCount / targetCount) * 100)}% COMPLETATO
                </div>
              </div>

              {/* Remaining spots */}
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-pink-500 mb-2">
                  {remainingSpots}
                </div>
                <div className="text-sm md:text-base text-muted-foreground font-medium">
                  POSTI RIMASTI
                </div>
              </div>
            </div>
          </div>

          {/* Benefits list */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8 text-left">
            <div className="flex items-center space-x-3 bg-white/60 rounded-lg p-4">
              <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-charcoal font-medium">Accesso anticipato alla piattaforma</span>
            </div>
            
            <div className="flex items-center space-x-3 bg-white/60 rounded-lg p-4">
              <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-charcoal font-medium">Sconti esclusivi fino al 50%</span>
            </div>
            
            <div className="flex items-center space-x-3 bg-white/60 rounded-lg p-4">
              <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-charcoal font-medium">Supporto prioritario 24/7</span>
            </div>
            
            <div className="flex items-center space-x-3 bg-white/60 rounded-lg p-4">
              <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-charcoal font-medium">Community esclusiva Telegram</span>
            </div>
          </div>

          {/* CTA Button */}
          <div className="flex flex-col items-center space-y-4">
            <ButtonJoinWaitingList 
              analyticsLabel="pioneers-cta"
              className="text-lg px-8 py-4 bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 transform hover:scale-105 transition-all duration-200 shadow-lg"
            />
            
            {/* Urgency indicator */}
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              <span>Solo {remainingSpots} posti disponibili</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PioneersSection;
