//
import React, { useEffect } from "react";
import AnnouncementBanner from "@/components/AnnouncementBanner";
import Header from "@/components/Header";
import HeroSection from "@/components/HeroSection";
import PioneersSection from "@/components/PioneersSection";
import ProblemSection from "@/components/ProblemSection";
import FeaturesSection from "@/components/FeaturesSection";
import SocialProof from "@/components/SocialProof";

import SecuritySection from "@/components/SecuritySection";
import RoadmapSection from "@/components/RoadmapSection";
import FAQSection from "@/components/FAQSection";
import Footer from "@/components/Footer";
import StickyCTA from "@/components/StickyCTA";
import { FlowSection } from "@/components/HowItWorks";
//let isInitialLoad = true;
const Index = () => {

  // if (isInitialLoad) {
  //   isInitialLoad = false;
  //   const delay = new Promise(resolve => setTimeout(resolve, 4000));
  //   throw delay; // This will trigger Suspense
  // }
  return (
  <>

        <HeroSection />
        {/* <PioneersSection /> */}
        {/* <ProblemSection /> */}
        <FeaturesSection />
        {/* <SocialProof /> */}
        <FlowSection />
        <SecuritySection />
        <FAQSection />
        {/*
        <RoadmapSection />
      */}
     </>
  );
};

export default Index;
